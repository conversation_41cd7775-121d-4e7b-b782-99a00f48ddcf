# 子域名代理和HTTPS支持说明

## 🚀 新功能概述

本次更新添加了两个重要功能：
1. **子域名代理支持** - 支持 `map.baidu.com`、`tieba.baidu.com` 等子域名的完整代理
2. **HTTPS协议支持** - 代理服务器可以通过HTTPS访问，提供更安全的连接

## 📋 子域名代理功能

### 工作原理

当页面中包含子域名链接时，系统会自动重写为代理链接：

**原始链接**：
```html
<a href="https://map.baidu.com/search?query=北京">百度地图</a>
<a href="https://tieba.baidu.com/f?kw=java">Java贴吧</a>
```

**重写后**：
```html
<a href="https://localhost:8080/search?query=北京&_subdomain=map">百度地图</a>
<a href="https://localhost:8080/f?kw=java&_subdomain=tieba">Java贴吧</a>
```

### 支持的子域名类型

- **百度系列**：`map.baidu.com`、`tieba.baidu.com`、`zhidao.baidu.com`、`wenku.baidu.com` 等
- **Google系列**：`mail.google.com`、`docs.google.com`、`drive.google.com` 等
- **任意网站**：只要配置了基础域名，所有子域名都会自动支持

### 测试子域名代理

1. **配置百度代理**：
```sql
INSERT INTO dwts_web_proxy_config (proxy_name, proxy_port, target_host, target_port, target_protocol, enable_page_watermark) 
VALUES ('百度代理', 8080, 'www.baidu.com', 443, 'https', 1);
```

2. **访问测试**：
```bash
# 访问主站
http://localhost:8080/

# 访问子域名（通过链接点击或直接访问）
http://localhost:8080/search?_subdomain=map&query=北京
http://localhost:8080/f?_subdomain=tieba&kw=java
```

## 🔒 HTTPS支持功能

### 启用HTTPS

1. **生成SSL证书**：
```bash
# Windows
generate-ssl-cert.bat

# Linux/Mac
keytool -genkeypair -alias tomcat -keyalg RSA -keysize 2048 \
  -storetype PKCS12 -keystore src/main/resources/keystore.p12 \
  -validity 365 -dname "CN=localhost, OU=DWTS, O=DWTS, L=Beijing, ST=Beijing, C=CN" \
  -storepass changeit -keypass changeit
```

2. **修改配置文件** (`application.yml`)：
```yaml
server:
  port: 9090
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12
    key-alias: tomcat
```

3. **重启应用**，然后通过HTTPS访问：
```
https://localhost:9090/
```

### HTTPS代理效果

启用HTTPS后，所有链接重写都会使用HTTPS协议：

**HTTP模式**：
```html
<a href="http://localhost:8080/search?_subdomain=map">百度地图</a>
```

**HTTPS模式**：
```html
<a href="https://localhost:9090/search?_subdomain=map">百度地图</a>
```

## 🧪 完整测试流程

### 测试1：HTTP子域名代理

1. 启动应用（HTTP模式）
2. 访问：`http://localhost:8080/`
3. 点击页面中的子域名链接
4. 验证链接是否正确代理到子域名

### 测试2：HTTPS子域名代理

1. 生成SSL证书：`generate-ssl-cert.bat`
2. 修改 `application.yml` 启用SSL
3. 重启应用
4. 访问：`https://localhost:9090/`（忽略证书警告）
5. 点击页面中的子域名链接
6. 验证链接使用HTTPS协议

### 测试3：子域名路由验证

直接访问子域名路由：
```bash
# 百度地图
https://localhost:9090/search?_subdomain=map&query=北京

# 百度贴吧  
https://localhost:9090/f?_subdomain=tieba&kw=java

# 百度知道
https://localhost:9090/question/123456?_subdomain=zhidao
```

## 🔧 技术实现细节

### 链接重写算法

1. **识别子域名**：使用正则表达式匹配 `subdomain.domain.com` 格式
2. **提取子域名**：将 `map.baidu.com` 中的 `map` 提取出来
3. **重写URL**：将原链接重写为 `proxy-host/path?_subdomain=map`
4. **路由处理**：代理服务器根据 `_subdomain` 参数构建正确的目标URL

### 协议自适应

```java
// 自动检测请求协议
String proxyProtocol = request.isSecure() ? "https" : "http";
String proxyHost = request.getServerName() + ":" + request.getLocalPort();

// 重写链接时使用正确的协议
String newLink = proxyProtocol + "://" + proxyHost + "/path";
```

### 内部参数过滤

```java
// 过滤内部参数，避免传递给目标服务器
private String filterInternalParams(String queryString) {
    return Arrays.stream(queryString.split("&"))
        .filter(param -> !param.startsWith("_subdomain="))
        .collect(Collectors.joining("&"));
}
```

## ⚠️ 注意事项

1. **自签名证书**：测试用的自签名证书会触发浏览器安全警告，生产环境请使用正式证书
2. **端口配置**：HTTPS默认使用443端口，HTTP使用80端口，测试时注意端口配置
3. **子域名限制**：某些网站可能有跨域限制，需要根据实际情况调整
4. **性能影响**：链接重写会增加一定的处理时间，大页面可能有轻微延迟

## 🎯 应用场景

1. **企业内网**：代理外部网站到内网，统一添加水印
2. **内容审计**：监控和记录用户访问的所有子域名内容
3. **安全防护**：通过HTTPS提供更安全的代理访问
4. **统一入口**：将多个子域名服务统一到一个代理入口

现在您可以完整地代理任何网站的所有子域名，并且支持HTTPS安全访问！🎉
