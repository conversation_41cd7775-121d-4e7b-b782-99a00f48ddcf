package com.wzsec.webproxy.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Pattern;

/**
 * Vue应用代理处理器
 * 处理Vue单页应用的特殊需求
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class VueProxyHandler {

    // Vue应用常见的API路径模式
    private static final Pattern[] API_PATTERNS = {
        Pattern.compile("^/api/.*"),
        Pattern.compile("^/rest/.*"),
        Pattern.compile("^/service/.*"),
        Pattern.compile("^/v\\d+/.*"),
        Pattern.compile(".*\\.json$"),
        Pattern.compile(".*\\.do$"),
        Pattern.compile(".*\\.action$")
    };

    // Vue应用静态资源模式
    private static final Pattern[] STATIC_PATTERNS = {
        Pattern.compile(".*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$"),
        Pattern.compile("^/static/.*"),
        Pattern.compile("^/assets/.*"),
        Pattern.compile("^/img/.*"),
        Pattern.compile("^/css/.*"),
        Pattern.compile("^/js/.*")
    };

    /**
     * 判断是否是API请求
     */
    public boolean isApiRequest(String path) {
        if (path == null) {
            return false;
        }
        
        for (Pattern pattern : API_PATTERNS) {
            if (pattern.matcher(path).matches()) {
                log.debug("识别为API请求: {}", path);
                return true;
            }
        }
        
        return false;
    }

    /**
     * 判断是否是静态资源请求
     */
    public boolean isStaticResource(String path) {
        if (path == null) {
            return false;
        }
        
        for (Pattern pattern : STATIC_PATTERNS) {
            if (pattern.matcher(path).matches()) {
                log.debug("识别为静态资源: {}", path);
                return true;
            }
        }
        
        return false;
    }

    /**
     * 判断是否是Vue页面请求
     */
    public boolean isVuePageRequest(String path, String contentType) {
        // 如果是静态资源或API，则不是页面请求
        if (isStaticResource(path) || isApiRequest(path)) {
            return false;
        }
        
        // 如果Content-Type是HTML，则是页面请求
        if (contentType != null && contentType.toLowerCase().contains("text/html")) {
            return true;
        }
        
        // 如果路径看起来像页面路径
        return path == null || path.equals("/") || !path.contains(".");
    }

    /**
     * 处理Vue应用的HTML内容
     * 主要是重写资源路径和API路径
     */
    public String processVueContent(String content, HttpServletRequest request, String targetHost, int targetPort) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }
        
        String proxyHost = "localhost:" + request.getLocalPort();
        String targetBaseUrl = "http://" + targetHost + ":" + targetPort;
        
        log.info("处理Vue内容 - 代理主机: {}, 目标: {}", proxyHost, targetBaseUrl);
        
        // 替换绝对路径为代理路径
        content = content.replaceAll("https?://" + Pattern.quote(targetHost + ":" + targetPort), 
                "http://" + proxyHost);
        
        // 处理相对路径的资源引用
        content = content.replaceAll("(src|href)=\"/([^\"]*?)\"", 
                "$1=\"http://" + proxyHost + "/$2\"");
        
        // 处理CSS中的url()引用
        content = content.replaceAll("url\\(['\"]?/([^'\"\\)]*)['\"]?\\)", 
                "url('http://" + proxyHost + "/$1')");
        
        // 处理JavaScript中的API调用
        content = processJavaScriptApiCalls(content, proxyHost);
        
        // 处理Vue Router的base路径
        content = content.replaceAll("(base:\\s*['\"])/([^'\"]*)['\"]", 
                "$1http://" + proxyHost + "/$2\"");
        
        // 处理axios baseURL配置
        content = content.replaceAll("(baseURL:\\s*['\"])/([^'\"]*)['\"]", 
                "$1http://" + proxyHost + "/$2\"");
        
        log.debug("Vue内容处理完成，内容长度: {}", content.length());
        
        return content;
    }

    /**
     * 处理JavaScript中的API调用
     */
    private String processJavaScriptApiCalls(String content, String proxyHost) {
        // 处理fetch调用 - 所有以/开头的路径
        content = content.replaceAll("fetch\\(['\"]/(\\w[^'\"]*)['\"]",
                "fetch('http://" + proxyHost + "/$1'");

        // 处理axios调用 - 所有以/开头的路径
        content = content.replaceAll("axios\\.(get|post|put|delete|patch|request)\\(['\"]/(\\w[^'\"]*)['\"]",
                "axios.$1('http://" + proxyHost + "/$2'");

        // 处理axios配置对象中的url
        content = content.replaceAll("(url:\\s*['\"])/([^'\"]*)['\"]",
                "$1http://" + proxyHost + "/$2\"");

        // 处理$.ajax调用
        content = content.replaceAll("(\\$\\.ajax\\([^}]*url:\\s*['\"])/([^'\"]*)['\"]",
                "$1http://" + proxyHost + "/$2\"");

        // 处理XMLHttpRequest
        content = content.replaceAll("(open\\([^,]*,\\s*['\"])/([^'\"]*)['\"]",
                "$1http://" + proxyHost + "/$2\"");

        // 处理request库调用
        content = content.replaceAll("(request\\([^}]*url:\\s*['\"])/([^'\"]*)['\"]",
                "$1http://" + proxyHost + "/$2\"");

        // 处理Vue中的$http调用
        content = content.replaceAll("(\\$http\\.(get|post|put|delete|patch)\\(['\"])/([^'\"]*)['\"]",
                "$1http://" + proxyHost + "/$3'");

        return content;
    }

    /**
     * 创建Vue应用的错误页面
     */
    public String createVueErrorPage(String error, HttpServletRequest request) {
        String proxyHost = "localhost:" + request.getLocalPort();
        
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>代理错误</title>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }\n" +
                "        .error-container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }\n" +
                "        .error-title { color: #e74c3c; font-size: 24px; margin-bottom: 20px; }\n" +
                "        .error-message { color: #666; margin-bottom: 20px; }\n" +
                "        .retry-btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"error-container\">\n" +
                "        <div class=\"error-title\">🚫 代理服务错误</div>\n" +
                "        <div class=\"error-message\">" + error + "</div>\n" +
                "        <div class=\"error-message\">请检查目标服务是否正常运行</div>\n" +
                "        <button class=\"retry-btn\" onclick=\"location.reload()\">重试</button>\n" +
                "        <button class=\"retry-btn\" onclick=\"location.href='http://" + proxyHost + "'\">返回首页</button>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }

    /**
     * 判断是否需要Vue特殊处理
     */
    public boolean needsVueProcessing(String targetHost, String path, String contentType) {
        // 如果目标主机包含常见的Vue应用端口或路径特征
        boolean isVueApp = targetHost != null && 
                          (targetHost.contains(":3000") || 
                           targetHost.contains(":8080") || 
                           targetHost.contains(":8081") || 
                           targetHost.contains(":8082") ||
                           targetHost.contains("vue") ||
                           targetHost.contains("frontend"));
        
        // 如果是HTML内容且可能是Vue应用
        boolean isHtmlContent = contentType != null && contentType.toLowerCase().contains("text/html");
        
        return isVueApp && isHtmlContent && isVuePageRequest(path, contentType);
    }
}
