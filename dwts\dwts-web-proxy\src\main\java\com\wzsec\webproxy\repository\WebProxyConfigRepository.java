package com.wzsec.webproxy.repository;

import com.wzsec.webproxy.domain.WebProxyConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Web代理配置Repository
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Repository
public interface WebProxyConfigRepository extends JpaRepository<WebProxyConfig, Long>, 
                                                 JpaSpecificationExecutor<WebProxyConfig> {

    /**
     * 根据代理端口查找配置
     *
     * @param proxyPort 代理端口
     * @return 代理配置
     */
    Optional<WebProxyConfig> findByProxyPortAndStatus(Integer proxyPort, String status);

    /**
     * 根据代理名称查找配置
     *
     * @param proxyName 代理名称
     * @return 代理配置
     */
    Optional<WebProxyConfig> findByProxyNameAndStatus(String proxyName, String status);

    /**
     * 查找所有激活的配置
     *
     * @return 激活的配置列表
     */
    List<WebProxyConfig> findByStatusOrderByProxyPortAsc(String status);

    /**
     * 检查端口是否已被使用
     *
     * @param proxyPort 代理端口
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    @Query("SELECT COUNT(w) > 0 FROM WebProxyConfig w WHERE w.proxyPort = :proxyPort " +
           "AND w.status = 'ACTIVE' AND (:excludeId IS NULL OR w.id != :excludeId)")
    boolean existsByProxyPortAndStatusActive(@Param("proxyPort") Integer proxyPort, 
                                           @Param("excludeId") Long excludeId);

    /**
     * 根据目标主机和端口查找配置
     *
     * @param targetHost 目标主机
     * @param targetPort 目标端口
     * @return 配置列表
     */
    List<WebProxyConfig> findByTargetHostAndTargetPortAndStatus(String targetHost, 
                                                               Integer targetPort, 
                                                               String status);

    /**
     * 查找启用水印的配置
     *
     * @return 启用水印的配置列表
     */
    @Query("SELECT w FROM WebProxyConfig w WHERE w.status = 'ACTIVE' " +
           "AND (w.enablePageWatermark = true OR w.enableApiWatermark = true)")
    List<WebProxyConfig> findActiveConfigsWithWatermarkEnabled();

    /**
     * 统计激活的配置数量
     *
     * @return 激活配置数量
     */
    long countByStatus(String status);

    /**
     * 根据代理端口范围查找配置
     *
     * @param startPort 起始端口
     * @param endPort   结束端口
     * @return 配置列表
     */
    @Query("SELECT w FROM WebProxyConfig w WHERE w.proxyPort BETWEEN :startPort AND :endPort " +
           "AND w.status = 'ACTIVE' ORDER BY w.proxyPort")
    List<WebProxyConfig> findByProxyPortBetweenAndStatus(@Param("startPort") Integer startPort,
                                                        @Param("endPort") Integer endPort);
}
