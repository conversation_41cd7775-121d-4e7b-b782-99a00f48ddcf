package com.wzsec.webproxy.config;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WebProxyConfigService;
import com.wzsec.webproxy.util.PortChecker;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.Connector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 多端口配置
 * 为每个代理配置创建额外的Tomcat连接器
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Configuration
public class MultiPortConfig {

    @Autowired
    private WebProxyConfigService configService;

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> containerCustomizer() {
        return factory -> {
            log.info("开始配置多端口监听...");

            try {
                // 从数据库获取代理配置（延迟获取）
                if (configService != null) {
                    List<WebProxyConfig> configs = configService.getAllActiveConfigs();
                    log.info("从数据库获取到{}个代理配置", configs.size());

                    for (WebProxyConfig config : configs) {
                        int port = config.getProxyPort();

                        // 跳过主应用端口
                        if (port == 9090) {
                            log.info("跳过主应用端口: {}", port);
                            continue;
                        }

                        // 创建额外的连接器
                        Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
                        connector.setPort(port);
                        connector.setScheme("http");
                        connector.setSecure(false);

                        // 设置连接器属性
                        connector.setProperty("maxThreads", "200");
                        connector.setProperty("maxConnections", "8192");
                        connector.setProperty("acceptCount", "100");
                        connector.setProperty("connectionTimeout", "20000");

                        factory.addAdditionalTomcatConnectors(connector);

                        log.info("添加代理端口连接器: {} -> {}:{} (端口:{})",
                                config.getProxyName(),
                                config.getTargetHost(),
                                config.getTargetPort(),
                                port);
                    }

                    log.info("多端口配置完成，共配置{}个额外端口", configs.size());
                } else {
                    log.warn("配置服务未就绪，将在应用启动后动态配置端口");
                }

            } catch (Exception e) {
                log.error("配置多端口失败，将使用单端口模式", e);
            }
        };
    }
}
