package com.wzsec.webproxy.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 百度代理特殊处理器
 * 处理百度网站的反代理机制和特殊需求
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class BaiduProxyHandler {

    @Autowired
    private UniversalProxyHandler universalProxyHandler;

    /**
     * 为百度请求创建特殊的请求头
     */
    public HttpHeaders createBaiduHeaders(HttpServletRequest request, String targetHost) {
        // 使用通用处理器创建基础请求头
        HttpHeaders headers = universalProxyHandler.createProxyHeaders(request, targetHost);

        // 百度特有的请求头设置
        headers.set("DNT", "1");
        headers.set("Connection", "keep-alive");
        headers.set("Upgrade-Insecure-Requests", "1");
        headers.set("Sec-Fetch-Dest", "document");
        headers.set("Sec-Fetch-Mode", "navigate");
        headers.set("Sec-Fetch-Site", "none");
        headers.set("Sec-Fetch-User", "?1");
        headers.set("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
        headers.set("sec-ch-ua-mobile", "?0");
        headers.set("sec-ch-ua-platform", "\"Windows\"");

        return headers;
    }

    /**
     * 处理百度响应内容（支持所有百度子域名）
     */
    public String processBaiduContent(String content, HttpServletRequest request, String targetHost, String targetProtocol) {
        if (content == null || content.trim().isEmpty()) {
            log.warn("百度响应内容为空");
            return content;
        }

        // 使用通用处理器处理内容，支持所有百度子域名
        content = universalProxyHandler.processContent(content, request, targetHost, targetProtocol);

        // 百度特有的额外处理
        content = processBaiduSpecificContent(content, request);

        log.debug("百度内容处理完成，内容长度: {}", content.length());

        return content;
    }

    /**
     * 处理百度特有的内容
     */
    private String processBaiduSpecificContent(String content, HttpServletRequest request) {
        String proxyHost = "localhost:" + request.getLocalPort();

        // 处理百度特有的JavaScript跳转
        content = content.replaceAll("window\\.location\\.replace\\(['\"]([^'\"]*)['\"]\\)",
                "window.location.replace('http://" + proxyHost + "$1')");

        // 处理百度搜索框的action
        content = content.replaceAll("(<form[^>]*action=['\"])/s(['\"][^>]*>)",
                "$1http://" + proxyHost + "/s$2");

        return content;
    }

    /**
     * 检查是否应该跳过某个请求头
     */
    private boolean shouldSkipHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("host") || 
               lowerName.equals("content-length") ||
               lowerName.startsWith("x-forwarded-") ||
               lowerName.equals("connection") ||
               lowerName.equals("upgrade") ||
               lowerName.equals("proxy-connection") ||
               lowerName.equals("transfer-encoding") ||
               lowerName.equals("te") ||
               lowerName.startsWith("sec-") ||
               lowerName.equals("origin") ||
               lowerName.equals("referer");
    }

    /**
     * 检查是否是百度相关的请求
     */
    public boolean isBaiduRequest(String targetHost) {
        return targetHost != null && 
               (targetHost.contains("baidu.com") || 
                targetHost.contains("百度"));
    }

    /**
     * 获取百度首页的默认内容（当请求失败时使用）
     */
    public String getBaiduFallbackContent(HttpServletRequest request) {
        String proxyHost = "localhost:" + request.getLocalPort();
        
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>百度一下，你就知道</title>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }\n" +
                "        .logo { font-size: 48px; color: #3385ff; margin-bottom: 30px; }\n" +
                "        .search-box { margin: 20px 0; }\n" +
                "        .search-input { width: 400px; height: 40px; font-size: 16px; padding: 0 10px; border: 2px solid #c4c7ce; }\n" +
                "        .search-btn { height: 44px; width: 100px; background: #3385ff; color: white; border: none; font-size: 16px; cursor: pointer; }\n" +
                "        .notice { color: #999; margin-top: 30px; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"logo\">百度</div>\n" +
                "    <div class=\"search-box\">\n" +
                "        <form action=\"http://" + proxyHost + "/s\" method=\"get\">\n" +
                "            <input type=\"text\" name=\"wd\" class=\"search-input\" placeholder=\"请输入搜索内容\">\n" +
                "            <input type=\"submit\" value=\"百度一下\" class=\"search-btn\">\n" +
                "        </form>\n" +
                "    </div>\n" +
                "    <div class=\"notice\">\n" +
                "        这是通过DWTS代理访问的百度页面<br>\n" +
                "        如果无法正常显示，请检查网络连接或联系管理员\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }
}
