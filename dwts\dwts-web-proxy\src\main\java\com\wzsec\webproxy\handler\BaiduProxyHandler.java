package com.wzsec.webproxy.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 百度代理特殊处理器
 * 处理百度网站的反代理机制和特殊需求
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class BaiduProxyHandler {

    @Autowired
    private UniversalProxyHandler universalProxyHandler;

    /**
     * 为百度请求创建特殊的请求头
     */
    public HttpHeaders createBaiduHeaders(HttpServletRequest request, String targetHost) {
        // 使用通用处理器创建基础请求头
        HttpHeaders headers = universalProxyHandler.createProxyHeaders(request, targetHost);

        // 百度特有的请求头设置
        headers.set("DNT", "1");
        headers.set("Connection", "keep-alive");
        headers.set("Upgrade-Insecure-Requests", "1");
        headers.set("Sec-Fetch-Dest", "document");
        headers.set("Sec-Fetch-Mode", "navigate");
        headers.set("Sec-Fetch-Site", "none");
        headers.set("Sec-Fetch-User", "?1");
        headers.set("sec-ch-ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
        headers.set("sec-ch-ua-mobile", "?0");
        headers.set("sec-ch-ua-platform", "\"Windows\"");

        return headers;
    }

    /**
     * 处理百度响应内容（委托给通用处理器）
     */
    public String processBaiduContent(String content, HttpServletRequest request, String targetHost, String targetProtocol) {
        return universalProxyHandler.processContent(content, request, targetHost, targetProtocol);
    }



    /**
     * 检查是否是百度相关的请求（包括所有子域名）
     */
    public boolean isBaiduRequest(String targetHost) {
        return universalProxyHandler.isDomainRequest(targetHost, "baidu.com") ||
               (targetHost != null && targetHost.contains("百度"));
    }

    /**
     * 获取百度首页的默认内容（当请求失败时使用）
     */
    public String getBaiduFallbackContent(HttpServletRequest request, String targetHost) {
        return universalProxyHandler.getDefaultErrorContent(request, "百度", targetHost);
    }
}
