package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WebProxyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Web代理配置管理控制器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/proxy/config")
public class WebProxyConfigController {

    @Autowired
    private WebProxyConfigService configService;

    /**
     * 获取所有代理配置
     */
    @GetMapping("/list")
    public ResponseEntity<List<WebProxyConfig>> getAllConfigs() {
        List<WebProxyConfig> configs = configService.getAllActiveConfigs();
        return ResponseEntity.ok(configs);
    }

    /**
     * 根据ID获取代理配置
     */
    @GetMapping("/{id}")
    public ResponseEntity<WebProxyConfig> getConfigById(@PathVariable Long id) {
        // 这里需要添加根据ID查询的方法
        return ResponseEntity.notFound().build();
    }

    /**
     * 根据端口获取代理配置
     */
    @GetMapping("/port/{port}")
    public ResponseEntity<WebProxyConfig> getConfigByPort(@PathVariable Integer port) {
        WebProxyConfig config = configService.getConfigByPort(port);
        if (config != null) {
            log.info("查询端口{}的配置: 名称={}, 页面水印={}, API水印={}, 水印文本={}, API路径模式={}",
                    port, config.getProxyName(),
                    config.getEnablePageWatermark(),
                    config.getEnableApiWatermark(),
                    config.getWatermarkText(),
                    config.getApiPathPatterns());
            return ResponseEntity.ok(config);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 创建代理配置
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createConfig(@Valid @RequestBody WebProxyConfig config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WebProxyConfig savedConfig = configService.saveConfig(config);
            result.put("success", true);
            result.put("message", "代理配置创建成功");
            result.put("data", savedConfig);
            
            log.info("创建代理配置成功: {} -> {}:{}", 
                    savedConfig.getProxyPort(), 
                    savedConfig.getTargetHost(), 
                    savedConfig.getTargetPort());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建代理配置失败: " + e.getMessage());
            
            log.error("创建代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 更新代理配置
     */
    @PutMapping("/update")
    public ResponseEntity<Map<String, Object>> updateConfig(@Valid @RequestBody WebProxyConfig config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WebProxyConfig updatedConfig = configService.updateConfig(config);
            result.put("success", true);
            result.put("message", "代理配置更新成功");
            result.put("data", updatedConfig);
            
            log.info("更新代理配置成功: {}", updatedConfig.getProxyName());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新代理配置失败: " + e.getMessage());
            
            log.error("更新代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 删除代理配置
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Map<String, Object>> deleteConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.deleteConfig(id);
            result.put("success", true);
            result.put("message", "代理配置删除成功");
            
            log.info("删除代理配置成功: {}", id);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除代理配置失败: " + e.getMessage());
            
            log.error("删除代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 启用代理配置
     */
    @PostMapping("/enable/{id}")
    public ResponseEntity<Map<String, Object>> enableConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.enableConfig(id);
            result.put("success", true);
            result.put("message", "代理配置启用成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "启用代理配置失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 禁用代理配置
     */
    @PostMapping("/disable/{id}")
    public ResponseEntity<Map<String, Object>> disableConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.disableConfig(id);
            result.put("success", true);
            result.put("message", "代理配置禁用成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "禁用代理配置失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取配置统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getConfigStats() {
        Map<String, Object> stats = configService.getConfigStats();
        return ResponseEntity.ok(stats);
    }

    /**
     * 刷新配置缓存
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshCache() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.refreshCache();
            result.put("success", true);
            result.put("message", "配置缓存刷新成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "刷新配置缓存失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }



    /**
     * 修复指定端口的配置，启用页面水印
     */
    @PostMapping("/fix-watermark/{port}")
    public ResponseEntity<Map<String, Object>> fixWatermarkConfig(@PathVariable Integer port) {
        Map<String, Object> result = new HashMap<>();

        try {
            WebProxyConfig config = configService.getConfigByPort(port);
            if (config == null) {
                result.put("success", false);
                result.put("message", "未找到端口 " + port + " 的配置");
                return ResponseEntity.ok(result);
            }

            //// 强制启用页面水印和API水印
            //config.setEnablePageWatermark(Boolean.TRUE);
            //config.setEnableApiWatermark(Boolean.TRUE);

            // 设置水印文本（如果为空）
            if (config.getWatermarkText() == null || config.getWatermarkText().trim().isEmpty()) {
                config.setWatermarkText("API代理_{IP}_{TIME}");
            }

            // 设置API路径模式（如果为空）
            if (config.getApiPathPatterns() == null || config.getApiPathPatterns().trim().isEmpty()) {
                config.setApiPathPatterns("/api/**,/rest/**,/service/**,/auth/**");
            }

            // 设置其他水印参数
            if (config.getWatermarkOpacity() == null) {
                config.setWatermarkOpacity(0.15);
            }
            if (config.getWatermarkColor() == null) {
                config.setWatermarkColor("#FF0000");
            }
            if (config.getWatermarkAngle() == null) {
                config.setWatermarkAngle(-30.0);
            }

            WebProxyConfig savedConfig = configService.saveConfig(config);

            result.put("success", true);
            result.put("message", "配置修复成功，已启用页面水印和API水印");
            result.put("data", savedConfig);

            log.info("修复端口{}的配置成功: 页面水印={}, API水印={}, 水印文本={}",
                    port, savedConfig.getEnablePageWatermark(),
                    savedConfig.getEnableApiWatermark(), savedConfig.getWatermarkText());

        } catch (Exception e) {
            log.error("修复端口{}的配置失败", port, e);
            result.put("success", false);
            result.put("message", "修复失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }



    /**
     * 检查API路径匹配
     */
    @PostMapping("/check-api-path")
    public ResponseEntity<Map<String, Object>> checkApiPath(
            @RequestParam Integer port,
            @RequestParam String path) {

        Map<String, Object> result = new HashMap<>();

        try {
            WebProxyConfig config = configService.getConfigByPort(port);
            if (config == null) {
                result.put("success", false);
                result.put("message", "未找到端口 " + port + " 的配置");
                return ResponseEntity.ok(result);
            }

            // 检查API路径模式
            String apiPatterns = config.getApiPathPatterns();
            result.put("apiPathPatterns", apiPatterns);
            result.put("enableApiWatermark", config.getEnableApiWatermark());

            // 检查路径是否匹配
            boolean matches = false;
            if (apiPatterns != null && !apiPatterns.trim().isEmpty()) {
                String[] patterns = apiPatterns.split(",");
                org.springframework.util.AntPathMatcher pathMatcher = new org.springframework.util.AntPathMatcher();

                for (String pattern : patterns) {
                    if (pattern != null && !pattern.trim().isEmpty()) {
                        boolean patternMatches = pathMatcher.match(pattern.trim(), path);
                        result.put("pattern_" + pattern.trim(), patternMatches);
                        if (patternMatches) {
                            matches = true;
                        }
                    }
                }
            }

            result.put("pathMatches", matches);
            result.put("success", true);

            if (!matches) {
                result.put("suggestion", "路径 '" + path + "' 不匹配任何API模式，请检查配置");
            }

        } catch (Exception e) {
            log.error("检查API路径匹配失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 安全地启用API水印（仅针对API路径）
     */
    @PostMapping("/enable-api-watermark/{port}")
    public ResponseEntity<Map<String, Object>> enableApiWatermarkSafely(@PathVariable Integer port) {
        Map<String, Object> result = new HashMap<>();

        try {
            WebProxyConfig config = configService.getConfigByPort(port);
            if (config == null) {
                result.put("success", false);
                result.put("message", "未找到端口 " + port + " 的配置");
                return ResponseEntity.ok(result);
            }

            // 设置更精确的API路径模式，避免页面被误识别
            String safeApiPatterns = "/api/**,/rest/**,/service/**,/auth/**,/oauth/**";
            config.setApiPathPatterns(safeApiPatterns);

            // 启用API水印
            config.setEnableApiWatermark(Boolean.TRUE);

            // 确保页面水印也启用（用于处理HTML页面）
            config.setEnablePageWatermark(Boolean.TRUE);

            // 设置水印参数
            if (config.getWatermarkText() == null || config.getWatermarkText().trim().isEmpty()) {
                config.setWatermarkText("API代理_{IP}_{TIME}");
            }
            if (config.getWatermarkOpacity() == null) {
                config.setWatermarkOpacity(0.15);
            }
            if (config.getWatermarkColor() == null) {
                config.setWatermarkColor("#FF0000");
            }
            if (config.getWatermarkAngle() == null) {
                config.setWatermarkAngle(-30.0);
            }

            WebProxyConfig savedConfig = configService.saveConfig(config);

            result.put("success", true);
            result.put("message", "API水印已安全启用，使用精确的路径匹配");
            result.put("data", savedConfig);
            result.put("apiPathPatterns", safeApiPatterns);

            log.info("安全启用API水印: 端口={}, 路径模式={}", port, safeApiPatterns);

        } catch (Exception e) {
            log.error("安全启用API水印失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 优化水印显示配置
     */
    @PostMapping("/optimize-watermark/{port}")
    public ResponseEntity<Map<String, Object>> optimizeWatermarkConfig(@PathVariable Integer port) {
        Map<String, Object> result = new HashMap<>();

        try {
            WebProxyConfig config = configService.getConfigByPort(port);
            if (config == null) {
                result.put("success", false);
                result.put("message", "未找到端口 " + port + " 的配置");
                return ResponseEntity.ok(result);
            }

            // 获取当前水印文本
            String watermarkText = config.getWatermarkText();
            if (watermarkText == null || watermarkText.trim().isEmpty()) {
                watermarkText = "DWTS水印_{IP}_{TIME}";
                config.setWatermarkText(watermarkText);
            }

            // 根据文本长度优化配置
            int textLength = calculateTextLength(watermarkText);

            // 动态设置水印参数
            if (textLength <= 10) {
                // 短文本：较大字体，较小尺寸
                config.setWatermarkWidth(250);
                config.setWatermarkHeight(120);
                config.setWatermarkOpacity(0.15);
            } else if (textLength <= 20) {
                // 中等文本：标准配置
                config.setWatermarkWidth(350);
                config.setWatermarkHeight(150);
                config.setWatermarkOpacity(0.12);
            } else {
                // 长文本：较大尺寸，较低透明度
                config.setWatermarkWidth(450);
                config.setWatermarkHeight(180);
                config.setWatermarkOpacity(0.10);
            }

            // 设置其他优化参数
            config.setWatermarkAngle(-25.0);  // 稍微调整角度
            config.setWatermarkColor("#FF0000");

            // 启用水印
            config.setEnablePageWatermark(Boolean.TRUE);

            WebProxyConfig savedConfig = configService.saveConfig(config);

            result.put("success", true);
            result.put("message", "水印显示配置已优化");
            result.put("data", savedConfig);
            result.put("textLength", textLength);
            result.put("optimizedSize", savedConfig.getWatermarkWidth() + "x" + savedConfig.getWatermarkHeight());

            log.info("优化水印配置: 端口={}, 文本长度={}, 尺寸={}x{}",
                    port, textLength, savedConfig.getWatermarkWidth(), savedConfig.getWatermarkHeight());

        } catch (Exception e) {
            log.error("优化水印配置失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 计算文本长度（中文字符按2个字符计算）
     */
    private int calculateTextLength(String text) {
        if (text == null) return 0;

        int length = 0;
        for (char c : text.toCharArray()) {
            if (c > 127) {
                // 中文或其他多字节字符
                length += 2;
            } else {
                // ASCII字符
                length += 1;
            }
        }
        return length;
    }
}
