package com.wzsec.webproxy.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.regex.Pattern;

/**
 * 通用代理处理器
 * 提供通用的代理处理功能，支持所有网站的子域名代理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class UniversalProxyHandler {

    /**
     * 为任意网站请求创建合适的请求头
     */
    public HttpHeaders createProxyHeaders(HttpServletRequest request, String targetHost) {
        HttpHeaders headers = new HttpHeaders();
        
        // 复制原始请求头，但跳过一些特殊头部
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            
            if (shouldSkipHeader(headerName)) {
                continue;
            }
            
            Enumeration<String> headerValues = request.getHeaders(headerName);
            while (headerValues.hasMoreElements()) {
                headers.add(headerName, headerValues.nextElement());
            }
        }
        
        // 设置目标主机的Host头
        headers.set("Host", targetHost);
        
        // 设置通用的浏览器请求头
        if (!headers.containsKey("User-Agent")) {
            headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        }
        
        if (!headers.containsKey("Accept")) {
            headers.set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        }
        
        if (!headers.containsKey("Accept-Language")) {
            headers.set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        }
        
        if (!headers.containsKey("Accept-Encoding")) {
            headers.set("Accept-Encoding", "gzip, deflate, br");
        }
        
        // 移除可能导致问题的代理相关头部
        headers.remove("X-Forwarded-For");
        headers.remove("X-Forwarded-Proto");
        headers.remove("X-Forwarded-Host");
        headers.remove("X-Real-IP");
        
        return headers;
    }

    /**
     * 处理网站响应内容，重写链接支持子域名
     */
    public String processContent(String content, HttpServletRequest request, String targetHost, String targetProtocol) {
        if (content == null || content.trim().isEmpty()) {
            log.warn("响应内容为空");
            return content;
        }
        
        String proxyHost = "localhost:" + request.getLocalPort();
        String baseDomain = extractBaseDomain(targetHost);
        
        log.debug("处理内容 - 代理主机: {}, 目标主机: {}, 基础域名: {}", proxyHost, targetHost, baseDomain);
        
        // 替换所有子域名的绝对链接
        content = replaceSubdomainLinks(content, baseDomain, targetProtocol, proxyHost);
        
        // 替换相对链接
        content = replaceRelativeLinks(content, proxyHost);
        
        // 处理JavaScript中的链接
        content = processJavaScriptLinks(content, proxyHost);
        
        // 处理CSS中的链接
        content = processCssLinks(content, proxyHost);
        
        log.debug("内容处理完成，内容长度: {}", content.length());
        
        return content;
    }

    /**
     * 替换子域名链接
     */
    private String replaceSubdomainLinks(String content, String baseDomain, String targetProtocol, String proxyHost) {
        // 匹配所有子域名的模式：*.baseDomain
        String subdomainPattern = "https?://[\\w\\-]+\\." + Pattern.quote(baseDomain);
        String replacement = "http://" + proxyHost;
        
        content = content.replaceAll(subdomainPattern, replacement);
        
        // 也处理主域名
        String mainDomainPattern = targetProtocol + "://" + Pattern.quote(baseDomain);
        content = content.replaceAll(mainDomainPattern, replacement);
        
        // 处理协议相对链接 //subdomain.domain.com
        String protocolRelativePattern = "//[\\w\\-]*\\." + Pattern.quote(baseDomain);
        content = content.replaceAll(protocolRelativePattern, "//" + proxyHost);
        
        return content;
    }

    /**
     * 替换相对链接
     */
    private String replaceRelativeLinks(String content, String proxyHost) {
        content = content.replaceAll("href=\"/", "href=\"http://" + proxyHost + "/");
        content = content.replaceAll("src=\"/", "src=\"http://" + proxyHost + "/");
        content = content.replaceAll("action=\"/", "action=\"http://" + proxyHost + "/");
        return content;
    }

    /**
     * 处理JavaScript中的链接
     */
    private String processJavaScriptLinks(String content, String proxyHost) {
        // 处理location.href赋值
        content = content.replaceAll("location\\.href\\s*=\\s*['\"]([^'\"]*)['\"]",
                "location.href = 'http://" + proxyHost + "$1'");

        // 处理window.location赋值
        content = content.replaceAll("window\\.location\\s*=\\s*['\"]([^'\"]*)['\"]",
                "window.location = 'http://" + proxyHost + "$1'");

        // 处理window.location.replace调用
        content = content.replaceAll("window\\.location\\.replace\\(['\"]([^'\"]*)['\"]\\)",
                "window.location.replace('http://" + proxyHost + "$1')");

        // 处理fetch调用
        content = content.replaceAll("fetch\\(['\"]/(\\w[^'\"]*)['\"]",
                "fetch('http://" + proxyHost + "/$1'");

        // 处理axios调用
        content = content.replaceAll("axios\\.(get|post|put|delete|patch|request)\\(['\"]/(\\w[^'\"]*)['\"]",
                "axios.$1('http://" + proxyHost + "/$2'");

        return content;
    }

    /**
     * 处理CSS中的链接
     */
    private String processCssLinks(String content, String proxyHost) {
        content = content.replaceAll("url\\(['\"]?/([^'\"\\)]*)['\"]?\\)", 
                "url('http://" + proxyHost + "/$1')");
        return content;
    }

    /**
     * 提取基础域名（去掉子域名）
     */
    public String extractBaseDomain(String host) {
        if (host == null) return null;
        
        String[] parts = host.split("\\.");
        if (parts.length >= 2) {
            // 返回最后两部分作为基础域名，如 baidu.com, google.com
            return parts[parts.length - 2] + "." + parts[parts.length - 1];
        }
        return host;
    }

    /**
     * 检查是否是指定域名的请求（包括子域名）
     */
    public boolean isDomainRequest(String targetHost, String domain) {
        if (targetHost == null || domain == null) return false;

        return targetHost.equals(domain) || targetHost.endsWith("." + domain);
    }

    /**
     * 检查是否是百度相关的请求（包括所有子域名）
     */
    public boolean isBaiduRequest(String targetHost) {
        return isDomainRequest(targetHost, "baidu.com") ||
               (targetHost != null && targetHost.contains("百度"));
    }

    /**
     * 检查是否应该跳过某个请求头
     */
    private boolean shouldSkipHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("host") || 
               lowerName.equals("content-length") ||
               lowerName.startsWith("x-forwarded-") ||
               lowerName.equals("connection") ||
               lowerName.equals("upgrade") ||
               lowerName.equals("proxy-connection") ||
               lowerName.equals("transfer-encoding") ||
               lowerName.equals("te") ||
               lowerName.startsWith("sec-") ||
               lowerName.equals("origin") ||
               lowerName.equals("referer");
    }

    /**
     * 获取网站的默认错误页面内容
     */
    public String getDefaultErrorContent(HttpServletRequest request, String siteName, String targetHost) {
        String proxyHost = "localhost:" + request.getLocalPort();
        
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>" + siteName + " - 代理访问</title>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }\n" +
                "        .logo { font-size: 48px; color: #3385ff; margin-bottom: 30px; }\n" +
                "        .notice { color: #999; margin-top: 30px; }\n" +
                "        .error { color: #ff4444; margin: 20px 0; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"logo\">" + siteName + "</div>\n" +
                "    <div class=\"error\">无法连接到目标网站: " + targetHost + "</div>\n" +
                "    <div class=\"notice\">\n" +
                "        这是通过DWTS代理访问的页面<br>\n" +
                "        如果无法正常显示，请检查网络连接或联系管理员\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }
}
