# 强制HTTPS代理功能说明

## 🔒 功能概述

现在所有代理后的链接都会强制使用HTTPS协议，无论原始请求是HTTP还是HTTPS。这提供了更高的安全性和一致性。

## 📋 工作原理

### 链接重写行为

**不管原始请求协议如何，所有重写的链接都使用HTTPS**：

**原始页面内容**：
```html
<!-- 原始链接可能是HTTP或HTTPS -->
<a href="http://map.baidu.com/search">百度地图</a>
<a href="https://tieba.baidu.com/f?kw=java">Java贴吧</a>
<script>
    location.href = "/api/data";
    fetch("/api/users");
</script>
<style>
    background: url('/images/bg.jpg');
</style>
```

**重写后（强制HTTPS）**：
```html
<!-- 所有链接都被重写为HTTPS -->
<a href="https://localhost:9090/search?_subdomain=map">百度地图</a>
<a href="https://localhost:9090/f?kw=java&_subdomain=tieba">Java贴吧</a>
<script>
    location.href = "https://localhost:9090/api/data";
    fetch("https://localhost:9090/api/users");
</script>
<style>
    background: url('https://localhost:9090/images/bg.jpg');
</style>
```

## 🧪 测试场景

### 场景1：HTTP请求 → HTTPS代理链接

```bash
# 用户通过HTTP访问代理
curl http://localhost:8080/

# 页面中的链接会被重写为HTTPS
<a href="https://localhost:8080/search?_subdomain=map">百度地图</a>
```

### 场景2：HTTPS请求 → HTTPS代理链接

```bash
# 用户通过HTTPS访问代理
curl https://localhost:9090/

# 页面中的链接同样是HTTPS
<a href="https://localhost:9090/search?_subdomain=map">百度地图</a>
```

### 场景3：混合内容处理

```html
<!-- 原始页面包含混合协议链接 -->
<a href="http://map.baidu.com/">HTTP地图</a>
<a href="https://tieba.baidu.com/">HTTPS贴吧</a>
<img src="/static/logo.png">

<!-- 重写后全部统一为HTTPS -->
<a href="https://localhost:9090/?_subdomain=map">HTTP地图</a>
<a href="https://localhost:9090/?_subdomain=tieba">HTTPS贴吧</a>
<img src="https://localhost:9090/static/logo.png">
```

## ⚙️ 配置要求

### 1. 启用HTTPS服务器

要让强制HTTPS链接正常工作，代理服务器本身需要支持HTTPS：

**生成SSL证书**：
```bash
# Windows
generate-ssl-cert.bat

# Linux/Mac  
keytool -genkeypair -alias tomcat -keyalg RSA -keysize 2048 \
  -storetype PKCS12 -keystore src/main/resources/keystore.p12 \
  -validity 365 -dname "CN=localhost" -storepass changeit -keypass changeit
```

**修改application.yml**：
```yaml
server:
  port: 9090
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12
    key-alias: tomcat
```

### 2. 代理配置示例

```sql
-- 百度代理配置
INSERT INTO dwts_web_proxy_config (
    proxy_name, proxy_port, target_host, target_port, target_protocol,
    enable_page_watermark, enable_api_watermark, watermark_text
) VALUES (
    '百度HTTPS代理', 8443, 'www.baidu.com', 443, 'https',
    1, 1, 'DWTS安全代理_{IP}_{TIME}'
);
```

## 🔍 验证方法

### 1. 检查链接重写

```bash
# 访问代理页面
curl -k https://localhost:9090/

# 检查响应中的链接是否都是HTTPS
grep -o 'https://localhost:9090[^"]*' response.html
```

### 2. 浏览器开发者工具

1. 打开浏览器访问：`https://localhost:9090/`
2. 按F12打开开发者工具
3. 查看Network标签页
4. 点击页面中的链接
5. 验证所有请求都使用HTTPS协议

### 3. 子域名路由测试

```bash
# 直接访问子域名路由
curl -k "https://localhost:9090/search?_subdomain=map&query=北京"

# 检查是否正确代理到 https://map.baidu.com/search?query=北京
```

## 🛡️ 安全优势

### 1. 数据传输加密
- 所有代理流量都通过HTTPS加密传输
- 防止中间人攻击和数据窃听
- 保护用户隐私和敏感信息

### 2. 统一安全策略
- 不管目标网站是否支持HTTPS，代理层统一提供加密
- 避免混合内容警告
- 提供一致的安全体验

### 3. 合规要求
- 满足企业安全合规要求
- 符合数据保护法规
- 提供审计友好的访问日志

## ⚠️ 注意事项

### 1. SSL证书配置
- 测试环境可以使用自签名证书
- 生产环境建议使用正式CA签发的证书
- 确保证书包含正确的域名信息

### 2. 性能考虑
- HTTPS会增加一定的CPU开销（加密/解密）
- 建议配置适当的连接池和缓存
- 监控服务器性能指标

### 3. 浏览器兼容性
- 现代浏览器都支持HTTPS
- 某些老旧浏览器可能有兼容性问题
- 建议提供HTTP到HTTPS的重定向

## 📊 使用效果

### 之前（协议不统一）：
```
用户请求: http://localhost:8080/
页面链接: http://localhost:8080/search?_subdomain=map  ❌ 不安全
```

### 现在（强制HTTPS）：
```
用户请求: http://localhost:8080/
页面链接: https://localhost:8080/search?_subdomain=map  ✅ 安全加密

用户请求: https://localhost:9090/
页面链接: https://localhost:9090/search?_subdomain=map  ✅ 安全加密
```

## 🎯 应用场景

1. **企业内网安全**：确保所有代理流量都加密传输
2. **合规审计**：满足安全合规要求
3. **统一安全策略**：不管目标网站协议，代理层统一HTTPS
4. **防护升级**：为不支持HTTPS的老旧网站提供加密层

现在您的代理系统提供了更高级别的安全保护！🔒✨
